// Modern Settings Component - Comic Website Design
// Using established color palette: #E83A3A primary red, gray neutrals

// Overlay and Modal Container
.settings-overlay {
  @apply fixed inset-0 z-[100] flex items-center justify-center;
  @apply bg-black/50 backdrop-blur-sm;
}

.settings-modal {
  @apply relative w-full h-full max-w-6xl max-h-[90vh] flex flex-col;
  @apply bg-white dark:bg-neutral-800;
  @apply rounded-2xl shadow-2xl border border-gray-200 dark:border-neutral-700;
  @apply overflow-hidden;
  @apply mx-4;

  // Responsive sizing
  @apply sm:w-[90vw] sm:h-[85vh] md:w-[80vw] lg:w-[75vw] xl:w-[70vw];
}

// Header Section
.settings-header {
  @apply flex items-center justify-between p-6;
  @apply border-b border-gray-200 dark:border-neutral-700;
  @apply bg-gray-50 dark:bg-neutral-900/50;
}

.settings-title-section {
  @apply flex items-center gap-4;
}

.settings-icon {
  @apply w-10 h-10 p-2 rounded-xl;
  @apply bg-primary-100 text-white;
  @apply shadow-md;

  svg {
    @apply w-full h-full;
  }
}

.settings-title-text {
  @apply flex flex-col;
}

.settings-title {
  @apply text-2xl font-bold text-gray-900 dark:text-white;
  @apply leading-tight;
}

.settings-subtitle {
  @apply text-sm text-gray-500 dark:text-gray-400;
  @apply mt-1;
}

// Search Section
.settings-search {
  @apply flex-1 max-w-md mx-8;
}

.search-input-wrapper {
  @apply relative;
}

.search-icon {
  @apply absolute left-3 top-1/2 -translate-y-1/2;
  @apply w-5 h-5 text-gray-400;
}

.search-input {
  @apply w-full pl-10 pr-4 py-3 rounded-xl;
  @apply bg-white dark:bg-neutral-700;
  @apply border border-gray-200 dark:border-neutral-600;
  @apply text-gray-900 dark:text-white;
  @apply placeholder-gray-500 dark:placeholder-gray-400;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-100 focus:border-transparent;

  &:hover {
    @apply border-gray-300 dark:border-neutral-500;
  }
}

// Close Button
.settings-close-button {
  @apply p-3 rounded-xl;
  @apply bg-gray-100 dark:bg-neutral-700;
  @apply hover:bg-gray-200 dark:hover:bg-neutral-600;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-100 focus:ring-offset-2;
  @apply hover:scale-105;

  svg {
    @apply w-5 h-5 text-gray-600 dark:text-gray-300;
  }

  &:active {
    @apply scale-95;
  }
}

// Main Content Area
.settings-content {
  @apply flex h-full min-h-0;
}

// Tab Navigation
.settings-tabs {
  @apply flex flex-col w-64 border-r border-gray-200 dark:border-neutral-700;
  @apply bg-gray-50 dark:bg-neutral-900/30;
  @apply p-4 gap-2;
}

.settings-tab {
  @apply flex items-center gap-3 p-4 rounded-xl;
  @apply hover:bg-white dark:hover:bg-neutral-700;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-100 focus:ring-offset-2;

  &.active {
    @apply bg-primary-100 text-white shadow-md;
    @apply hover:bg-primary-200;

    .tab-icon svg {
      @apply text-white;
    }
  }

  &:not(.active) {
    @apply text-gray-700 dark:text-gray-200;

    .tab-icon svg {
      @apply text-gray-500 dark:text-gray-400;
    }
  }
}

.tab-icon {
  @apply w-6 h-6 flex-shrink-0;

  svg {
    @apply w-full h-full;
  }
}

.tab-label {
  @apply font-medium text-sm;
}

// Settings Panel
.settings-panel {
  @apply flex-1 overflow-y-auto;
  @apply p-6;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-neutral-600 rounded-full;
  }

  &::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-neutral-500;
  }
}

// Group Header
.settings-group-header {
  @apply flex items-center justify-between mb-8;
  @apply pb-4 border-b border-gray-200 dark:border-neutral-700;
}

.group-title {
  @apply text-2xl font-bold text-gray-900 dark:text-white;
}

.group-description {
  @apply text-sm text-gray-500 dark:text-gray-400 mt-1 hidden md:block;
}

.reset-button {
  @apply flex items-center gap-2 px-4 py-2 rounded-xl;
  @apply bg-gray-100 dark:bg-neutral-700;
  @apply hover:bg-gray-200 dark:hover:bg-neutral-600;
  @apply text-gray-700 dark:text-gray-200;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-100 focus:ring-offset-2;

  svg {
    @apply w-4 h-4;
  }

  &:hover {
    @apply text-primary-100;
  }
}

// Settings List
.settings-list {
  @apply space-y-6;
}

.setting-item {
  @apply flex items-start justify-between gap-6;
  @apply p-6 rounded-2xl;
  @apply bg-white dark:bg-neutral-800;
  @apply border border-gray-200 dark:border-neutral-700;
  @apply shadow-sm hover:shadow-md;

  &:hover {
    @apply border-gray-300 dark:border-neutral-600;
    transform: translateY(-1px);
  }
}

.setting-info {
  @apply flex-1;
}

.setting-label {
  @apply block text-lg font-semibold text-gray-900 dark:text-white;
  @apply mb-2 cursor-pointer;
}

.setting-description {
  @apply text-sm text-gray-500 dark:text-gray-400;
  @apply leading-relaxed;
}

.setting-control {
  @apply flex-shrink-0;
  @apply min-w-[200px];
}

// Setting Input Styles
.setting-selection {
  @apply w-full;
}

.setting-color-wrapper {
  @apply flex items-center gap-3;
}

.setting-color-input {
  @apply w-12 h-12 rounded-xl border-2 border-gray-200 dark:border-neutral-600;
  @apply cursor-pointer;
  @apply hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-100 focus:ring-offset-2;

  &::-webkit-color-swatch-wrapper {
    @apply p-0 border-none rounded-xl overflow-hidden;
  }

  &::-webkit-color-swatch {
    @apply border-none rounded-xl;
  }
}

.setting-color-value {
  @apply text-sm font-mono text-gray-600 dark:text-gray-300;
  @apply bg-gray-100 dark:bg-neutral-700;
  @apply px-3 py-2 rounded-lg;
}

// Empty State
.settings-empty {
  @apply flex flex-col items-center justify-center;
  @apply py-16 text-center;
}

.empty-icon {
  @apply w-16 h-16 mb-4;
  @apply text-gray-400 dark:text-gray-500;

  svg {
    @apply w-full h-full;
  }
}

.empty-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-2;
}

.empty-description {
  @apply text-gray-500 dark:text-gray-400;
  @apply max-w-md;
}

// Backdrop
.settings-backdrop {
  @apply fixed inset-0 bg-black/50 backdrop-blur-sm -z-50;
}

// Responsive Design
@media (max-width: 768px) {
  .settings-modal {
    @apply w-full h-full max-w-none max-h-none;
    @apply rounded-none mx-0;
  }

  .settings-header {
    @apply flex-col gap-4 p-4;
  }

  .settings-search {
    @apply max-w-none mx-0 w-full;
  }

  .settings-content {
    @apply flex-col;
  }

  .settings-tabs {
    @apply flex-row w-full border-r-0 border-b;
    @apply overflow-x-auto p-2;
    @apply gap-1;
  }

  .settings-tab {
    @apply flex-shrink-0 min-w-[120px];
    @apply p-3;
  }

  .tab-label {
    @apply text-xs;
  }

  .setting-item {
    @apply flex-col gap-4;
  }

  .setting-control {
    @apply min-w-0 w-full;
  }
}
