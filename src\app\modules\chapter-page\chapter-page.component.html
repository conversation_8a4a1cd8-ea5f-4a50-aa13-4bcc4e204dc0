<!-- Header Section -->
<header class="header-section">
  <div class="header-container">
    <app-breadcrumb
      class="breadcrumb-wrapper"
      [Links]="[
        { label: 'Trang chủ', url: '/' },
        {
          label: comic.title,
          url: '/truyen-tranh/' + comic.url + '-' + comic.id
        },
        { label: mainChapter.title, url: '' }
      ]"
    >
    </app-breadcrumb>
    <app-anouncement></app-anouncement>
  </div>
</header>

<!-- Main Container -->
<div class="main-container" #screenContainer>
  <!-- Chapter Header -->
  <section #HeaderContainer class="chapter-header-container">
    <div class="chapter-header-card">
      <!-- Chapter Info -->
      <div class="chapter-info-section">
        <!-- Report Error Button -->
        <button class="report-error-button" (click)="reportError()">
          <svg class="report-icon" viewBox="0 0 24 24">
            <path
              d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"
            />
            <line x1="12" y1="9" x2="12" y2="13" />
            <line x1="12" y1="17" x2="12.01" y2="17" />
          </svg>
          <span class="report-text">Báo lỗi</span>
        </button>

        <!-- Comic Title -->
        <div class="comic-title-section">
          <h1 class="comic-title">
            <a
              [routerLink]="['/truyen-tranh', comic.url + '-' + comic.id]"
              [title]="comic.title"
              class="comic-title-link"
            >
              {{ comic.title }}
            </a>
          </h1>
        </div>

        <!-- Chapter Details -->
        <div class="chapter-details">
          <h2 class="chapter-title">{{ mainChapter.title }}</h2>
          <time class="chapter-date" [dateTime]="mainChapter.updateAt | date : 'yyyy-MM-dd'">
            Đăng lúc: {{ mainChapter.updateAt | date : 'dd/MM/yyyy' }}
          </time>
        </div>
      </div>

      <!-- Server Selection -->
      <div class="server-selection-section">
        <div class="server-list">
          <button
            *ngFor="
              let serverId of showAllServers
                ? listChapterServerIds
                : (listChapterServerIds ?? []).slice(0, 3);
              let i = index
            "
            (click)="changeServer(serverId, i)"
            class="server-button"
            [class.server-button-active]="i === selectedServerId"
          >
            <svg class="server-icon" viewBox="0 0 24 24">
              <path d="M7 18a4.6 4.4 0 0 1 0 -9h0a5 4.5 0 0 1 11 2h1a3.5 3.5 0 0 1 0 7h-12" />
            </svg>
            <span class="server-text">Server {{ i + 1 }}</span>
          </button>

          <button
            (click)="showMoreServer()"
            *ngIf="listChapterServerIds && listChapterServerIds.length > 3"
            class="server-expand-button"
          >
            <svg
              class="expand-icon"
              [class.expand-icon-rotated]="showAllServers"
              viewBox="0 0 24 24"
            >
              <path d="M18 15l-6-6l-6 6h12" />
            </svg>
          </button>
        </div>
      </div>

      <div id="position-btn"></div>
    </div>

    <!-- Control Bar -->
    <nav #controlBar appFadeIn [duration]="1000" class="control-bar">
      <div *ngIf="isBrowser" class="control-bar-content">
        <!-- Home Button -->
        <div class="control-group">
          <a href="" title="Trang chủ" class="control-button control-button-home">
            <svg class="control-icon" viewBox="0 0 24 24">
              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
              <polyline points="9,22 9,12 15,12 15,22" />
            </svg>
          </a>
        </div>

        <!-- Fullscreen Button -->
        <div class="control-group">
          <button title="Toàn màn hình" class="control-button" (click)="enterFullscreen()">
            <svg class="control-icon" viewBox="0 0 24 24">
              <path
                d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"
              />
            </svg>
          </button>
        </div>

        <!-- Chapter Navigation -->
        <div class="chapter-navigation-group">
          <button
            class="nav-button nav-button-prev"
            (click)="navigateChapter(true)"
            aria-label="Chương trước"
            [disabled]="isLoading"
            [class.nav-button-active]="
              listChapters.length > 0 &&
              mainChapter.slug !== listChapters![listChapters!.length - 1].slug
            "
          >
            <svg class="nav-icon" viewBox="0 0 24 24">
              <polyline points="15 18 9 12 15 6" />
            </svg>
          </button>

          <div class="chapter-selector-wrapper">
            <app-chapter-selector
              [chapters]="listChapters"
              [mainChapter]="mainChapter"
              [topToBottom]="TopToBottom"
              (chapterChange)="OnChangeChapter($event)"
            >
            </app-chapter-selector>
          </div>

          <button
            class="nav-button nav-button-next"
            (click)="navigateChapter(false)"
            aria-label="Chương tiếp"
            [disabled]="isLoading"
            [class.nav-button-active]="
              listChapters.length > 0 && mainChapter.slug !== listChapters![0].slug
            "
          >
            <svg class="nav-icon" viewBox="0 0 24 24">
              <polyline points="9 18 15 12 9 6" />
            </svg>
          </button>
        </div>

        <!-- Zoom Controls -->
        <div (appClickOutside)="zoomPanel.classList.remove('zoom-panel-active')" class="control-group zoom-group">
          <button title="Thu phóng" class="control-button zoom-button">
            <svg
              (click)="ZoomImage(!isLimitZoom); zoomPanel.classList.add('zoom-panel-active')"
              class="control-icon"
              viewBox="0 0 24 24"
            >
              @if (!isLimitZoom) {
              <circle cx="11" cy="11" r="8" />
              <line x1="21" y1="21" x2="16.65" y2="16.65" />
              <line x1="11" y1="8" x2="11" y2="14" />
              <line x1="8" y1="11" x2="14" y2="11" />
              } @else {
              <circle cx="11" cy="11" r="8" />
              <line x1="21" y1="21" x2="16.65" y2="16.65" />
              <line x1="8" y1="11" x2="14" y2="11" />
              }
            </svg>
            <div #zoomPanel class="zoom-panel">
              <div class="zoom-info">
                <span class="zoom-percentage">{{ getZoomPercentage() }}%</span>
                <div class="zoom-controls">
                  <button class="zoom-control-btn" (click)="ZoomImage(false)" title="Thu nhỏ">
                    <svg class="zoom-control-icon" viewBox="0 0 24 24">
                      <line x1="5" y1="12" x2="19" y2="12" />
                    </svg>
                  </button>
                  <button class="zoom-control-btn" (click)="ZoomImage(true)" title="Phóng to">
                    <svg class="zoom-control-icon" viewBox="0 0 24 24">
                      <line x1="12" y1="5" x2="12" y2="19" />
                      <line x1="5" y1="12" x2="19" y2="12" />
                    </svg>
                  </button>
                </div>
              </div>
  
              <button (click)="resetView()" title="Đặt lại" class="zoom-reset-btn">
                <svg class="zoom-reset-icon" viewBox="0 0 24 24">
                  <path
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
              </button>
            </div>
          </button>

          <!-- Zoom Panel -->
        </div>

        <!-- Settings Button -->
        <div class="control-group">
          <button
            (click)="ToggleMenu(!isToggle)"
            title="Cài đặt"
            class="control-button settings-button"
            #ToggleMenuButton
          >
            <svg class="control-icon" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="3" />
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Settings Menu -->
      <div *ngIf="isBrowser" #MenuNavigation class="settings-menu">
        <div class="settings-menu-content">
          <div class="settings-option">
            <h3 class="settings-option-title">Hướng đọc</h3>
            <div class="settings-option-buttons">
              <button
                class="settings-btn"
                [class.settings-btn-active]="isVertical"
                (click)="changeDirectionReading(true)"
              >
                <svg class="settings-btn-icon" viewBox="0 0 24 24">
                  <rect x="6" y="2" width="12" height="20" rx="2" />
                  <line x1="12" y1="6" x2="12" y2="10" />
                  <line x1="12" y1="14" x2="12" y2="18" />
                </svg>
                <span class="settings-btn-text">Đọc dọc</span>
              </button>

              <button
                class="settings-btn"
                [class.settings-btn-active]="!isVertical"
                (click)="changeDirectionReading(false)"
              >
                <svg class="settings-btn-icon" viewBox="0 0 24 24">
                  <rect x="2" y="6" width="20" height="12" rx="2" />
                  <line x1="6" y1="12" x2="10" y2="12" />
                  <line x1="14" y1="12" x2="18" y2="12" />
                </svg>
                <span class="settings-btn-text">Đọc ngang</span>
              </button>
            </div>
          </div>

          <div class="settings-option">
            <h3 class="settings-option-title">Tự động chuyển chương</h3>
            <label class="settings-toggle">
              <input
                type="checkbox"
                class="settings-toggle-input"
                (change)="onCheckboxChange($event)"
                [checked]="isAutoNextChapter"
              />
              <div class="settings-toggle-slider">
                <div class="settings-toggle-thumb"></div>
              </div>
              <span class="settings-toggle-label">
                {{ isAutoNextChapter ? 'Bật' : 'Tắt' }}
              </span>
            </label>
          </div>

          <div class="settings-option">
            <h3 class="settings-option-title">Chế độ ban đêm</h3>
            <button
              class="settings-btn settings-btn-full"
              [class.settings-btn-active]="isNightMode"
              (click)="enableNightLight(!isNightMode)"
            >
              <svg class="settings-btn-icon" viewBox="0 0 24 24">
                <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" />
              </svg>
              <span class="settings-btn-text">
                {{ isNightMode ? 'Tắt chế độ ban đêm' : 'Bật chế độ ban đêm' }}
              </span>
            </button>
          </div>
        </div>
      </div>
    </nav>
  </section>

  <!-- Banners -->
  @if(isBrowser && !isLoading) {
  <div class="banner-section">
    <app-banner2 [codes]="['2051269', '2051272']"></app-banner2>
    <app-banner3></app-banner3>
  </div>
  }

  <!-- Reading Container -->
  <section class="reading-container" #readingContainer>
    <div #imageContainer id="image-container" class="reading-content">
      <!-- Scroll Navigation -->
      <div class="scroll-navigation" [class.scroll-navigation-hidden]="isVertical || isFullScreen">
        <button
          (click)="scrollHorizontal(-1)"
          class="scroll-btn scroll-btn-prev"
          title="Trang trước"
        >
          <svg class="scroll-btn-icon" viewBox="0 0 24 24">
            <polyline points="15 18 9 12 15 6" />
          </svg>
          <span class="scroll-btn-text">Trước</span>
        </button>

        <button (click)="scrollHorizontal(1)" class="scroll-btn scroll-btn-next" title="Trang tiếp">
          <span class="scroll-btn-text">Tiếp</span>
          <svg class="scroll-btn-icon" viewBox="0 0 24 24">
            <polyline points="9 18 15 12 9 6" />
          </svg>
        </button>
      </div>

      <!-- Loading State -->
      <div *ngIf="isLoading" class="loading-container">
        <div class="loading-content">
          <div class="loading-spinner">
            <svg class="loading-icon" viewBox="0 0 24 24">
              <circle class="loading-circle-bg" cx="12" cy="12" r="10" />
              <circle class="loading-circle-progress" cx="12" cy="12" r="10" />
            </svg>
          </div>
          <div class="loading-text">
            <h3 class="loading-title">Đang tải chương...</h3>
            <p class="loading-subtitle">Vui lòng đợi trong giây lát</p>
          </div>
        </div>

        <!-- Loading Skeleton -->
        <div class="loading-skeleton">
          <div class="skeleton-page" *ngFor="let page of [1, 2, 3, 4, 5]"></div>
        </div>
      </div>

      <!-- Chapter Images -->
      <ng-container *ngIf="isBrowser && !isLoading">
        <div
          *ngFor="let page of listImgs; let i = index; trackBy: trackByPageId"
          class="chapter-page-container"
          [style.width]="this.defaultWidth * (1 + zoomLevel) + 'px'"
        >
          <img
            [loading]="i <= 5 ? 'eager' : 'lazy'"
            class="chapter-page-image"
            [alt]="comic.url + 'chapter-page-' + (i + 1)"
            #chapterPageImage
            [ngClass]="{
              'chapter-page-horizontal': !isVertical,
              'night-mode': isNightMode
            }"
            [src]="page.url"
            (error)="onError($event)"
            (load)="onLoad($event)"
          />
        </div>
      </ng-container>
    </div>

    <!-- End Chapter Navigation -->
    <div #EndChapter class="end-chapter-navigation">
      <div class="end-chapter-content">
        <button
          class="end-nav-button end-nav-prev"
          (click)="navigateChapter(true)"
          aria-label="Chương trước"
          [disabled]="isLoading"
          [class.end-nav-active]="
            listChapters.length > 0 &&
            mainChapter.slug !== listChapters![listChapters!.length - 1].slug
          "
        >
          <svg class="end-nav-icon" viewBox="0 0 24 24">
            <polyline points="15 18 9 12 15 6" />
          </svg>
          <span class="end-nav-text">Chương trước</span>
        </button>

        <div class="end-chapter-info">
          <h3 class="end-chapter-title">Kết thúc chương</h3>
          <p class="end-chapter-subtitle">{{ mainChapter.title }}</p>
        </div>

        <button
          class="end-nav-button end-nav-next"
          (click)="navigateChapter(false)"
          aria-label="Chương tiếp theo"
          [disabled]="isLoading"
          [class.end-nav-active]="
            listChapters.length > 0 && mainChapter.slug !== listChapters![0].slug
          "
        >
          <span class="end-nav-text">Chương tiếp</span>
          <svg class="end-nav-icon" viewBox="0 0 24 24">
            <polyline points="9 18 15 12 9 6" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Bottom Banners -->
    @if(isBrowser && !isLoading) {
    <div class="banner-section">
      <app-banner2 [codes]="['2052076', '2052074']"></app-banner2>
    </div>
    }

    <!-- Comments -->
    @if (isBrowser) {
    <app-comment #commentComponent [comicId]="comic.id" [chapterID]="mainChapter.id"></app-comment>
    }
  </section>

  <!-- Scroll to Top Button -->
  <button
    (click)="scrollToTop($event)"
    class="scroll-to-top-btn"
    [class.scroll-to-top-visible]="showScrollToTop"
    title="Lên đầu trang"
    type="button"
  >
    <svg class="scroll-to-top-icon" viewBox="0 0 24 24">
      <polyline points="18 15 12 9 6 15" />
    </svg>
    <span class="scroll-to-top-text">Lên đầu</span>
  </button>
</div>
